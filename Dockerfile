FROM nvidia/cuda:12.2.0-runtime-ubuntu22.04

# Set working directory
WORKDIR /app

# Install system packages
RUN apt-get update && \
    apt-get install -y ffmpeg python3 python3-pip python3-venv git gcc libglib2.0-0 libsm6 libxext6 && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# Install uv
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Copy app
COPY . /app

# Install Python deps with uv
RUN uv sync --frozen --no-cache

# Run the app
CMD ["uv", "run", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8400"]
