import os
import tempfile
import shutil
import mimetypes

ALLOWED_MIME_TYPES = {"audio/mpeg", "audio/wav", "audio/x-wav", "audio/mp4", "audio/x-m4a"}

def save_upload_file(upload_file) -> str:
    """Save uploaded file to temp location and return path."""
    suffix = os.path.splitext(upload_file.filename)[-1]
    with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp:
        shutil.copyfileobj(upload_file.file, tmp)
        return tmp.name

def validate_audio(file) -> bool:
    """Validate MIME type."""
    mime_type, _ = mimetypes.guess_type(file.filename)
    return mime_type in ALLOWED_MIME_TYPES
