from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
import asyncio
import torch
import whisperx
import logging
from utils import save_upload_file, validate_audio
import os

app = FastAPI(title="WhisperX Word Timestamp API", version="1.0")

# Setup logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("whisperx_api")

# Load models at startup
device = "cuda" if torch.cuda.is_available() else "cpu"
model = whisperx.load_model("large-v2", device=device)
alignment_model = None
alignment_metadata = None

@app.on_event("startup")
async def load_alignment():
    global alignment_model, alignment_metadata
    logger.info("Loading alignment model...")
    alignment_model, alignment_metadata = whisperx.load_align_model(language_code="en", device=device)
    logger.info("Alignment model loaded.")

@app.post("/transcribe/")
async def transcribe_audio(file: UploadFile = File(...)):
    if not validate_audio(file):
        raise HTTPException(status_code=400, detail="Unsupported audio file type.")

    # Save file
    temp_path = save_upload_file(file)
    logger.info(f"File saved at {temp_path}")

    try:
        # Async transcription & alignment in thread
        result = await asyncio.to_thread(process_audio, temp_path)
        return JSONResponse(content=result)
    except Exception as e:
        logger.exception("Transcription failed.")
        raise HTTPException(status_code=500, detail=f"Transcription failed: {e}")
    finally:
        if os.path.exists(temp_path):
            os.remove(temp_path)
            logger.info(f"Temporary file {temp_path} removed.")

def process_audio(path: str):
    audio = whisperx.load_audio(path)
    result = model.transcribe(audio, batch_size=16)

    # Get language-specific alignment model (already loaded on startup)
    result_aligned = whisperx.align(
        result["segments"], alignment_model, alignment_metadata, audio, device, return_char_alignments=False
    )

    return {
        "text": result["text"],
        "word_timestamps": result_aligned["word_segments"]
    }
